
#include "stdafx.h"
#include "SWDCalib.h"
#include "Resect.h"
#include <iostream>
#include <fstream>

// This program will link against the existing object files of the project
// to use the CResect class and its methods directly.

int main(int argc, char* argv[]) {
    if (argc != 3) {
        std::cerr << "Usage: " << argv[0] << " <input.dst> <output.txt>" << std::endl;
        return 1;
    }

    // Initialize MFC
    if (!AfxWinInit(::GetModuleHandle(NULL), NULL, ::GetCommandLine(), 0)) {
        std::cerr << "Error: MFC Failed to initialize!" << std::endl;
        return 1;
    }

    CString input_path = CString(argv[1]);
    std::ofstream out_file(argv[2]);

    if (!out_file.is_open()) {
        std::cerr << "Error: Could not open output file " << argv[2] << std::endl;
        return 1;
    }

    CResect resect_obj;
    
    // We call the method that reads the .dst/.jdc file.
    // The second parameter (bRawIcf) is set to TRUE as a default guess.
    if (resect_obj.ReadCalibrationDst(input_path, TRUE)) {
        out_file << "--- Successfully Parsed File ---" << std::endl;
        out_file << "Distortion Parameters (NewDistortPrm):" << std::endl;
        out_file << "  Focal Length (eFk): " << resect_obj.m_DistortPrm.eFk << std::endl;
        out_file << "  Width: " << resect_obj.m_DistortPrm.width << std::endl;
        out_file << "  Height: " << resect_obj.m_DistortPrm.height << std::endl;
        out_file << "  Pixel Size: " << resect_obj.m_DistortPrm.pixel << std::endl;
        out_file << "  Principal Point X (eXp): " << resect_obj.m_DistortPrm.eXp << std::endl;
        out_file << "  Principal Point Y (eYp): " << resect_obj.m_DistortPrm.eYp << std::endl;
        out_file << "  k1: " << resect_obj.m_DistortPrm.m_eK[0] << std::endl;
        out_file << "  k2: " << resect_obj.m_DistortPrm.m_eK[1] << std::endl;
        out_file << "  k3: " << resect_obj.m_DistortPrm.m_eK[2] << std::endl;
        out_file << "  p1: " << resect_obj.m_DistortPrm.m_eP[0] << std::endl;
        out_file << "  p2: " << resect_obj.m_DistortPrm.m_eP[1] << std::endl;
        out_file << "  b1 (affine): " << resect_obj.m_DistortPrm.m_eB[0] << std::endl;
        out_file << "  b2 (affine): " << resect_obj.m_DistortPrm.m_eB[1] << std::endl;
        out_file << std::endl;

    } else {
        out_file << "--- Failed to Parse File ---" << std::endl;
        std::cerr << "Error: ReadCalibrationDst failed." << std::endl;
    }

    out_file.close();
    return 0;
}
