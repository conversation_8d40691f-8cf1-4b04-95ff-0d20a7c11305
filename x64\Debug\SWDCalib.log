﻿生成启动时间为 2025/6/4 14:45:19。
     1>项目“D:\WRHsoftware\JXDC\JXDC-VC10-WRH\SWDCalib\SWDCalib.vcxproj”在节点 2 上(build 个目标)。
     1>InitializeBuildStatus:
         正在创建“x64\Debug\SWDCalib.unsuccessfulbuild”，因为已指定“AlwaysCreate”。
       ClCompile:
         C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\x86_amd64\CL.exe /c /ID:\openCV\opencv\build\include /Zi /nologo /W3 /WX- /Od /D WIN32 /D _WINDOWS /D _DEBUG /D _MBCS /Gm /EHsc /RTC1 /MTd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Yc"StdAfx.h" /Fp"x64\Debug\SWDCalib.pch" /Fo"x64\Debug\\" /Fd"x64\Debug\vc100.pdb" /Gd /TP /errorReport:prompt stdafx.cpp
         stdafx.cpp
         C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\x86_amd64\CL.exe /c /ID:\openCV\opencv\build\include /Zi /nologo /W3 /WX- /Od /D WIN32 /D _WINDOWS /D _DEBUG /D _MBCS /Gm /EHsc /RTC1 /MTd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Yu"StdAfx.h" /Fp"x64\Debug\SWDCalib.pch" /Fo"x64\Debug\\" /Fd"x64\Debug\vc100.pdb" /Gd /TP /errorReport:prompt ..\JXDCCmn.cpp ..\WZDDRDLG.CPP DSPDlg.cpp GetCameraNumber.cpp JXDCCalibrateDlg.cpp Resect.cpp SaveResult.cpp SWDCalib.cpp tinyxml2.cpp
         tinyxml2.cpp
         SWDCalib.cpp
     1>d:\wrhsoftware\jxdc\jxdc-vc10-wrh\swdcalib\resect.h(18): error C2146: 语法错误: 缺少“;”(在标识符“m_aFsEyDstrPm”的前面)
     1>d:\wrhsoftware\jxdc\jxdc-vc10-wrh\swdcalib\resect.h(18): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
     1>d:\wrhsoftware\jxdc\jxdc-vc10-wrh\swdcalib\resect.h(18): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
     1>d:\wrhsoftware\jxdc\jxdc-vc10-wrh\swdcalib\resect.h(19): error C2146: 语法错误: 缺少“;”(在标识符“m_bFsEyDstrPm”的前面)
     1>d:\wrhsoftware\jxdc\jxdc-vc10-wrh\swdcalib\resect.h(19): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
     1>d:\wrhsoftware\jxdc\jxdc-vc10-wrh\swdcalib\resect.h(19): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
     1>SWDCalib.cpp(258): warning C4996: 'strcpy': This function or variable may be unsafe. Consider using strcpy_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
                 C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\include\string.h(105) : 参见“strcpy”的声明
     1>SWDCalib.cpp(264): warning C4996: 'strcpy': This function or variable may be unsafe. Consider using strcpy_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
                 C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\include\string.h(105) : 参见“strcpy”的声明
     1>SWDCalib.cpp(268): warning C4996: 'strcpy': This function or variable may be unsafe. Consider using strcpy_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
                 C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\include\string.h(105) : 参见“strcpy”的声明
         SaveResult.cpp
         Resect.cpp
     1>Resect.cpp(6): fatal error C1083: 无法打开包括文件:“opencv2\opencv.hpp”: No such file or directory
         JXDCCalibrateDlg.cpp
     1>d:\wrhsoftware\jxdc\jxdc-vc10-wrh\swdcalib\resect.h(18): error C2146: 语法错误: 缺少“;”(在标识符“m_aFsEyDstrPm”的前面)
     1>d:\wrhsoftware\jxdc\jxdc-vc10-wrh\swdcalib\resect.h(18): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
     1>d:\wrhsoftware\jxdc\jxdc-vc10-wrh\swdcalib\resect.h(18): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
     1>d:\wrhsoftware\jxdc\jxdc-vc10-wrh\swdcalib\resect.h(19): error C2146: 语法错误: 缺少“;”(在标识符“m_bFsEyDstrPm”的前面)
     1>d:\wrhsoftware\jxdc\jxdc-vc10-wrh\swdcalib\resect.h(19): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
     1>d:\wrhsoftware\jxdc\jxdc-vc10-wrh\swdcalib\resect.h(19): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
     1>JXDCCalibrateDlg.cpp(995): error C2065: “wFishEyeFsxVersion”: 未声明的标识符
     1>JXDCCalibrateDlg.cpp(996): error C2065: “wFishEyeDistortFileVer”: 未声明的标识符
     1>JXDCCalibrateDlg.cpp(997): error C2065: “FishEyeDistortPrm”: 未声明的标识符
     1>JXDCCalibrateDlg.cpp(997): error C2070: “'unknown-type'”: 非法的 sizeof 操作数
     1>JXDCCalibrateDlg.cpp(1053): error C2039: “m_aFsEyDstrPm”: 不是“CResect”的成员
                 d:\wrhsoftware\jxdc\jxdc-vc10-wrh\swdcalib\resect.h(7) : 参见“CResect”的声明
     1>JXDCCalibrateDlg.cpp(1053): error C2065: “FishEyeDistortPrm”: 未声明的标识符
     1>JXDCCalibrateDlg.cpp(1053): error C2070: “'unknown-type'”: 非法的 sizeof 操作数
     1>JXDCCalibrateDlg.cpp(1054): error C2039: “m_bFsEyDstrPm”: 不是“CResect”的成员
                 d:\wrhsoftware\jxdc\jxdc-vc10-wrh\swdcalib\resect.h(7) : 参见“CResect”的声明
     1>JXDCCalibrateDlg.cpp(1054): error C2065: “FishEyeDistortPrm”: 未声明的标识符
     1>JXDCCalibrateDlg.cpp(1054): error C2070: “'unknown-type'”: 非法的 sizeof 操作数
     1>JXDCCalibrateDlg.cpp(1091): error C2039: “width”: 不是“NewDistortPrm”的成员
                 d:\wrhsoftware\jxdc\jxdc-vc10-wrh\NewDistortPrm.h(7) : 参见“NewDistortPrm”的声明
     1>JXDCCalibrateDlg.cpp(1092): error C2039: “height”: 不是“NewDistortPrm”的成员
                 d:\wrhsoftware\jxdc\jxdc-vc10-wrh\NewDistortPrm.h(7) : 参见“NewDistortPrm”的声明
     1>JXDCCalibrateDlg.cpp(1093): error C2039: “pixel”: 不是“NewDistortPrm”的成员
                 d:\wrhsoftware\jxdc\jxdc-vc10-wrh\NewDistortPrm.h(7) : 参见“NewDistortPrm”的声明
     1>JXDCCalibrateDlg.cpp(1209): error C2039: “m_aFsEyDstrPm”: 不是“CResect”的成员
                 d:\wrhsoftware\jxdc\jxdc-vc10-wrh\swdcalib\resect.h(7) : 参见“CResect”的声明
     1>JXDCCalibrateDlg.cpp(1209): error C2065: “FishEyeDistortPrm”: 未声明的标识符
     1>JXDCCalibrateDlg.cpp(1209): error C2070: “'unknown-type'”: 非法的 sizeof 操作数
     1>JXDCCalibrateDlg.cpp(1210): error C2039: “m_bFsEyDstrPm”: 不是“CResect”的成员
                 d:\wrhsoftware\jxdc\jxdc-vc10-wrh\swdcalib\resect.h(7) : 参见“CResect”的声明
     1>JXDCCalibrateDlg.cpp(1210): error C2065: “FishEyeDistortPrm”: 未声明的标识符
     1>JXDCCalibrateDlg.cpp(1210): error C2070: “'unknown-type'”: 非法的 sizeof 操作数
     1>JXDCCalibrateDlg.cpp(1246): error C2039: “width”: 不是“NewDistortPrm”的成员
                 d:\wrhsoftware\jxdc\jxdc-vc10-wrh\NewDistortPrm.h(7) : 参见“NewDistortPrm”的声明
     1>JXDCCalibrateDlg.cpp(1247): error C2039: “height”: 不是“NewDistortPrm”的成员
                 d:\wrhsoftware\jxdc\jxdc-vc10-wrh\NewDistortPrm.h(7) : 参见“NewDistortPrm”的声明
     1>JXDCCalibrateDlg.cpp(1248): error C2039: “pixel”: 不是“NewDistortPrm”的成员
                 d:\wrhsoftware\jxdc\jxdc-vc10-wrh\NewDistortPrm.h(7) : 参见“NewDistortPrm”的声明
         GetCameraNumber.cpp
         DSPDlg.cpp
         WZDDRDLG.CPP
     1>..\WZDDRDLG.CPP(69): warning C4996: 'strcpy': This function or variable may be unsafe. Consider using strcpy_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
                 C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\include\string.h(105) : 参见“strcpy”的声明
         JXDCCmn.cpp
         Generating Code...
     1>已完成生成项目“D:\WRHsoftware\JXDC\JXDC-VC10-WRH\SWDCalib\SWDCalib.vcxproj”(build 个目标)的操作 - 失败。

生成失败。

已用时间 00:00:04.05
