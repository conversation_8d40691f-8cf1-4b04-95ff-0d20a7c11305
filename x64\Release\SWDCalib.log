﻿生成启动时间为 2025/7/29 15:04:08。
     1>项目“A:\Program_ImageProcess\JXDC-VC10-WRH\SWDCalib\SWDCalib.vcxproj”在节点 2 上(build 个目标)。
     1>InitializeBuildStatus:
         正在对“x64\Release\SWDCalib.unsuccessfulbuild”执行 Touch 任务。
       ClCompile:
         所有输出均为最新。
         所有输出均为最新。
       ResourceCompile:
         所有输出均为最新。
       Link:
         C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\x86_amd64\link.exe /ERRORREPORT:PROMPT /OUT:"A:\Program_ImageProcess\JXDC-VC10-WRH\SWDCalib\x64\Release\SWDCalib.exe" /INCREMENTAL:NO /NOLOGO /LIBPATH:A:\Program_ImageProcess\zhng_vs10_20230119\Opencv231\StaticBuild\3rdparty\lib\x64 /LIBPATH:A:\Program_ImageProcess\zhng_vs10_20230119\Opencv231\build\x64\vc10\staticlib zlib.lib libjasper.lib opencv_calib3d231.lib opencv_features2d231.lib opencv_flann231.lib opencv_core231.lib opencv_imgproc231.lib opencv_highgui231.lib /NODEFAULTLIB:LIBCMT.lib /MANIFEST /ManifestFile:"x64\Release\SWDCalib.exe.intermediate.manifest" /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /DEBUG /PDB:"A:\Program_ImageProcess\JXDC-VC10-WRH\SWDCalib\x64\Release\SWDCalib.pdb" /SUBSYSTEM:WINDOWS /OPT:REF /OPT:ICF /LTCG /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"A:\Program_ImageProcess\JXDC-VC10-WRH\SWDCalib\x64\Release\SWDCalib.lib" /MACHINE:X64 x64\Release\SWDCalib.res
         x64\Release\JXDCCmn.obj
         x64\Release\WZDDRDLG.obj
         x64\Release\DSPDlg.obj
         x64\Release\GetCameraNumber.obj
         x64\Release\JXDCCalibrateDlg.obj
         x64\Release\Resect.obj
         x64\Release\SaveResult.obj
         x64\Release\stdafx.obj
         x64\Release\SWDCalib.obj
         x64\Release\tinyxml2.obj
         正在生成代码
         已完成代码的生成
         SWDCalib.vcxproj -> A:\Program_ImageProcess\JXDC-VC10-WRH\SWDCalib\x64\Release\SWDCalib.exe
       Manifest:
         C:\Program Files (x86)\Microsoft SDKs\Windows\v7.0A\bin\mt.exe /nologo /verbose /outputresource:"A:\Program_ImageProcess\JXDC-VC10-WRH\SWDCalib\x64\Release\SWDCalib.exe;#1" /manifest x64\Release\SWDCalib.exe.intermediate.manifest "C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\Include\Manifest\dpiaware.manifest"
       FinalizeBuildStatus:
         正在删除文件“x64\Release\SWDCalib.unsuccessfulbuild”。
         正在对“x64\Release\SWDCalib.lastbuildstate”执行 Touch 任务。
     1>已完成生成项目“A:\Program_ImageProcess\JXDC-VC10-WRH\SWDCalib\SWDCalib.vcxproj”(build 个目标)的操作。

生成成功。

已用时间 00:00:04.48
