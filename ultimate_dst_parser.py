import struct
import sys

# Based on the analysis of JXDCCalibrateDlg.cpp, the .dst/.jdc file is a complex
# binary archive. However, the same file also contains logic to write a .wml (XML) file
# with the same data. We can use the order of XML writing operations to deduce the
# likely order of members in the binary structures.

# This script assumes the binary file provided (.dst) is actually a .jdc file
# or a file with a compatible structure.

def parse_jdc_file(binary_file_path, output_txt_path):
    """Parses the .jdc file and writes its contents to a text file."""
    output_lines = []

    try:
        with open(binary_file_path, "rb") as f:
            # --- 1. Read IntegralParamHead ---
            # struct IntegralParamHead { USHORT wSdxVersion, wDstVersion, wSizeDistortPrm; };
            output_lines.append("--- File Header (IntegralParamHead) ---")
            header_data = f.read(6) # 3 * USHORT (2 bytes each)
            if len(header_data) < 6:
                raise ValueError("Incomplete file header.")
            wSdxVersion, wDstVersion, wSizeDistortPrm = struct.unpack('<3H', header_data)
            output_lines.append(f"wSdxVersion: {wSdxVersion}")
            output_lines.append(f"wDstVersion: {wDstVersion}")
            output_lines.append(f"wSizeDistortPrm: {wSizeDistortPrm}")
            output_lines.append("")

            # --- 2. Read number of cameras (nResect) ---
            # CArchive stores object counts as a 16-bit or 32-bit integer.
            # Let's try reading 4 bytes first for safety.
            nResect_data = f.read(4)
            if len(nResect_data) < 4:
                raise ValueError("Could not read number of cameras.")
            nResect = struct.unpack('<I', nResect_data)[0]
            output_lines.append(f"Number of Cameras (nResect): {nResect}")
            output_lines.append("="*40)

            # --- 3. Loop through each camera's data ---
            for i in range(nResect):
                output_lines.append(f"\n--- Camera {i+1} Data ---")

                # CArchive serializes CString by writing length then characters.
                # This is complex to parse without MFC's code. We will *assume* the filename
                # is written in a simple format for this script, but it might fail here.
                # Let's try to read a length-prefixed string (common serialization)
                try:
                    str_len = struct.unpack('<B', f.read(1))[0] # Assume single-byte length
                    cam_name = f.read(str_len).decode('ascii')
                    output_lines.append(f"Camera Data File Name: {cam_name}")
                    # Read the newline character written after the string
                    f.read(1) # Skip newline
                except Exception:
                     output_lines.append("Warning: Could not reliably parse camera name string.")
                     # We must seek forward based on the known struct sizes to recover.

                # --- 3a. Read NewDistortPrm ---
                # From XML writing order in JXDCCalibrateDlg.cpp:L1100-L1115
                # struct NewDistortPrm { double eFk, eXp, eYp, m_eK[3], m_eP[2], m_eB[2]; int width, height; double pixel; };
                # Total: 8 doubles (64 bytes) + 2 ints (8 bytes) + 1 double (8 bytes) = 80 bytes
                distort_data = f.read(80)
                if len(distort_data) < 80:
                    raise ValueError(f"Incomplete distortion data for camera {i+1}")
                
                distort_params = struct.unpack('<dii8d', distort_data)
                output_lines.append("Distortion Parameters (NewDistortPrm):")
                output_lines.append(f"  Focal Length (eFk): {distort_params[0]}")
                output_lines.append(f"  Width: {distort_params[1]}")
                output_lines.append(f"  Height: {distort_params[2]}")
                output_lines.append(f"  Pixel Size: {distort_params[3]}")
                output_lines.append(f"  Principal Point X (eXp): {distort_params[4]}")
                output_lines.append(f"  Principal Point Y (eYp): {distort_params[5]}")
                output_lines.append(f"  k1: {distort_params[6]}")
                output_lines.append(f"  k2: {distort_params[7]}")
                output_lines.append(f"  k3: {distort_params[8]}")
                output_lines.append(f"  p1: {distort_params[9]}")
                output_lines.append(f"  p2: {distort_params[10]}")
                # The XML code doesn't show b1 and b2, but the struct likely has them.
                # The struct size implies they exist.

                # --- 3b. Read Vignetting Data (CVntAprtrSht) ---
                # First, an integer count 'k' is written
                k_data = f.read(4)
                k = struct.unpack('<I', k_data)[0]
                output_lines.append(f"Vignetting Block Count: {k}")
                for j in range(k):
                    vignette_data = f.read(56) # sizeof(CVntAprtrSht) = 4*8 + 4 + 8 + 4*4 = 56 (approx)
                    # This part is complex, skipping detailed parsing for now.
                    output_lines.append(f"  [Vignetting Block {j+1} read, size {len(vignette_data)} bytes]")

                # --- 3c. Read Collineation Parameters (CCollineParm) ---
                # This is written multiple times. We'll parse the first block.
                # struct CCollineParm { CPoint3D pointCamera; double rotateAngle[3]; ... };
                # CPoint3D is 3 doubles. rotateAngle is 3 doubles. Total = 6 doubles = 48 bytes for the start.
                f.read(4) # Skip the rotation integer
                colline_data = f.read(48) 
                if len(colline_data) < 48:
                    raise ValueError(f"Incomplete CCollineParm data for camera {i+1}")
                
                colline_params = struct.unpack('<6d', colline_data)
                output_lines.append("Exterior Orientation (CCollineParm - Normal Fly):")
                output_lines.append(f"  Xs: {colline_params[0]}")
                output_lines.append(f"  Ys: {colline_params[1]}")
                output_lines.append(f"  Zs: {colline_params[2]}")
                output_lines.append(f"  Phi (radians): {colline_params[3]}")
                output_lines.append(f"  Omega (radians): {colline_params[4]}")
                output_lines.append(f"  Kappa (radians): {colline_params[5]}")

                # The file contains more CCollineParm blocks, we will skip them for this parsing task
                f.seek(48 * 3, 1) # Skip the 3 other collineation blocks

        with open(output_txt_path, 'w', encoding='utf-8') as f_out:
            f_out.write('\n'.join(output_lines))
        
        print(f"Successfully parsed {binary_file_path} to {output_txt_path}")

    except FileNotFoundError:
        print(f"Error: File not found at {binary_file_path}", file=sys.stderr)
        sys.exit(1)
    except Exception as e:
        print(f"An error occurred during parsing: {e}", file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    src_file = "/mnt/d/WRHsoftware/JXDC/JXDC-VC10-WRH/SWDCalib/P18单机平台/CamA-328-202506.dst"
    out_file = "/mnt/d/WRHsoftware/JXDC/JXDC-VC10-WRH/SWDCalib/P18单机平台/out.txt"
    parse_jdc_file(src_file, out_file)
