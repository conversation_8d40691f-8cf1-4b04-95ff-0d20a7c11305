{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Debug SWDCalib (x64)",
            "type": "cppvsdbg",
            "request": "launch",
            "program": "${workspaceFolder}/x64/Release/SWDCalib.exe", // 替换为你的输出路径
            "args": [],
            "stopAtEntry": false,
            "cwd": "${workspaceFolder}",
            "environment": [],
            "console": "externalTerminal",
            "preLaunchTask": "Build SWDCalib (Release x64)" // 必须与 tasks.json 的 label 一致
        }
    ]
}