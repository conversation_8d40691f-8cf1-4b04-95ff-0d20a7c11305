import struct
import sys

def parse_dst_file(binary_file_path, output_txt_path):
    """
    Parses a .dst binary file based on the reverse-engineered structure
    and writes the content to a text file.
    """
    output_content = []
    block_num = 0
    try:
        with open(binary_file_path, "rb") as f_in:
            while True:
                # Each block starts with a 4-byte integer (m_nBands)
                chunk = f_in.read(4)
                if not chunk:
                    break  # End of file

                block_num += 1
                output_content.append(f"--- Block {block_num} ---")

                # Unpack m_nBands (int)
                m_nBands = struct.unpack('<i', chunk)[0]
                output_content.append(f"m_nBands: {m_nBands}")

                # The C++ source indicates m_nBands should be between 1 and 4
                if not (0 < m_nBands <= 4):
                    output_content.append(f"Warning: Invalid m_nBands value ({m_nBands}). Stopping parse.")
                    break

                # Unpack the CVignettingParam array
                output_content.append("m_VignettingParam Array:")
                for i in range(m_nBands):
                    # Each struct is 6 doubles (48 bytes)
                    param_chunk = f_in.read(48)
                    if len(param_chunk) < 48:
                        raise IOError("Incomplete data for CVignettingParam struct")
                    
                    params = struct.unpack('<6d', param_chunk)
                    output_content.append(f"  [Param {i+1}]:")
                    output_content.append(f"    m_exCen: {params[0]}")
                    output_content.append(f"    m_eyCen: {params[1]}")
                    output_content.append(f"    m_eFactor[0]: {params[2]}")
                    output_content.append(f"    m_eFactor[1]: {params[3]}")
                    output_content.append(f"    m_eFactor[2]: {params[4]}")
                    output_content.append(f"    m_eFactor[3]: {params[5]}")

                # Unpack m_eAperture (double, 8 bytes)
                aperture_chunk = f_in.read(8)
                if len(aperture_chunk) < 8:
                    raise IOError("Incomplete data for m_eAperture")
                m_eAperture = struct.unpack('<d', aperture_chunk)[0]
                output_content.append(f"m_eAperture: {m_eAperture}")

                # Unpack m_uShutter (UINT, 4 bytes)
                shutter_chunk = f_in.read(4)
                if len(shutter_chunk) < 4:
                    raise IOError("Incomplete data for m_uShutter")
                m_uShutter = struct.unpack('<I', shutter_chunk)[0]
                output_content.append(f"m_uShutter: {m_uShutter}")
                output_content.append("") # Blank line for readability

        # Write the parsed content to the output text file
        with open(output_txt_path, "w", encoding="utf-8") as f_out:
            f_out.write('\n'.join(output_content))
        
        print(f"Successfully parsed {binary_file_path} and saved results to {output_txt_path}")

    except FileNotFoundError:
        print(f"Error: The file {binary_file_path} was not found.", file=sys.stderr)
        sys.exit(1)
    except Exception as e:
        print(f"An error occurred: {e}", file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    # The paths are hardcoded for this specific request
    src_path = r"/mnt/d/WRHsoftware/JXDC/JXDC-VC10-WRH/SWDCalib/P18单机平台/CamA-328-202506.dst"
    dest_path = r"/mnt/d/WRHsoftware/JXDC/JXDC-VC10-WRH/SWDCalib/P18单机平台/out.txt"
    parse_dst_file(src_path, dest_path)
